# src/gui/grain_analysis_widget_optimized.py
"""
Optimized version of grain_analysis_widget.py that integrates the new
OptimizedGrainStorage system for efficient grain deletion and management.

Key optimizations:
1. Individual grain storage instead of monolithic NPZ files
2. JSON index for fast grain tracking
3. Efficient deletion without full recalculation
4. Backward compatibility with existing projects
"""

import os
import logging
import time
from typing import Set, Optional, Dict, Any
from datetime import datetime

import pandas as pd
import torch
from PySide6.QtWidgets import QMessageBox
from PySide6.QtCore import Qt, QMetaObject, Q_ARG

# Import the optimized storage system
from src.core.optimized_grain_storage import OptimizedGrainStorage

logger = logging.getLogger(__name__)

class OptimizedGrainAnalysisWidget:
    """
    Mixin class that provides optimized grain storage functionality.
    This can be integrated into the existing GrainAnalysisWidget.
    """
    
    def __init__(self):
        # Initialize optimized storage
        self.optimized_storage = None
        self.use_optimized_storage = True  # Flag to enable/disable optimization
        self.grain_id_mapping = {}  # Maps DataFrame indices to grain IDs
        
    def _init_optimized_storage(self, image_id: str) -> bool:
        """
        Initialize optimized storage for the current image.
        
        Args:
            image_id: Unique identifier for the current image
            
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            if not hasattr(self, 'project') or not self.project:
                logger.warning("Cannot initialize optimized storage: No project available")
                return False
                
            base_dir = os.path.join(self.project.temp_dir, "state")
            self.optimized_storage = OptimizedGrainStorage(base_dir, image_id)
            logger.info(f"Initialized optimized storage for image {image_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize optimized storage: {e}")
            return False
    
    def delete_selected_grains_optimized(self, indices_to_delete: Set[int]) -> bool:
        """
        Optimized grain deletion that avoids full recalculation and resaving.
        
        Args:
            indices_to_delete: Set of DataFrame indices to delete
            
        Returns:
            True if deletion successful, False otherwise
        """
        if not indices_to_delete or self.grain_df is None or self.grain_annotations is None:
            return False
            
        num_to_delete = len(indices_to_delete)
        
        # Confirm deletion with user
        reply = QMessageBox.question(
            self, "Confirm Deletion", 
            f"Delete {num_to_delete} selected grain(s)?\n\n"
            f"Using optimized deletion (no recalculation required).",
            QMessageBox.Yes | QMessageBox.Cancel, 
            QMessageBox.Cancel
        )
        
        if reply == QMessageBox.Cancel:
            return False
            
        logger.info(f"Starting optimized deletion of {num_to_delete} grains: {indices_to_delete}")
        start_time = time.time()
        
        try:
            # Validate indices
            valid_indices_to_delete = indices_to_delete.intersection(set(self.grain_df.index))
            if not valid_indices_to_delete:
                logger.warning("No valid indices to delete")
                return False
            
            # Get grain IDs for the indices to delete
            grain_ids_to_delete = set()
            for idx in valid_indices_to_delete:
                if idx in self.grain_id_mapping:
                    grain_ids_to_delete.add(self.grain_id_mapping[idx])
            
            # Update status
            self.update_status(f"Deleting {len(valid_indices_to_delete)} grains (optimized)...")
            
            # Method 1: Use optimized storage if available
            if self.optimized_storage and grain_ids_to_delete:
                success = self.optimized_storage.delete_grains(grain_ids_to_delete)
                if success:
                    # Update in-memory data structures
                    self._update_memory_after_deletion(valid_indices_to_delete)
                    
                    # Update visualization without full rebuild
                    self._update_visualization_after_deletion(valid_indices_to_delete)
                    
                    elapsed_time = time.time() - start_time
                    logger.info(f"Optimized deletion completed in {elapsed_time:.3f}s")
                    self.update_status(f"Deleted {len(valid_indices_to_delete)} grains in {elapsed_time:.3f}s")
                    
                    return True
                else:
                    logger.warning("Optimized storage deletion failed, falling back to legacy method")
            
            # Method 2: Fallback to legacy deletion but with optimizations
            return self._delete_grains_legacy_optimized(valid_indices_to_delete)
            
        except Exception as e:
            logger.exception(f"Error in optimized grain deletion: {e}")
            self.update_status(f"Deletion failed: {e}")
            return False
    
    def _update_memory_after_deletion(self, indices_to_delete: Set[int]):
        """
        Update in-memory data structures after grain deletion.
        
        Args:
            indices_to_delete: Set of DataFrame indices that were deleted
        """
        # Create mask for rows to keep
        df_keep_mask = ~self.grain_df.index.isin(indices_to_delete)
        original_df_indices = self.grain_df.index.tolist()
        
        # Update annotations
        indices_to_keep_in_annotations = [
            i for i, df_idx in enumerate(original_df_indices) 
            if df_idx not in indices_to_delete
        ]
        
        if isinstance(self.grain_annotations, torch.Tensor):
            if indices_to_keep_in_annotations:
                self.grain_annotations = self.grain_annotations[indices_to_keep_in_annotations]
            else:
                self.grain_annotations = torch.empty(
                    (0,) + self.grain_annotations.shape[1:], 
                    dtype=self.grain_annotations.dtype, 
                    device=self.grain_annotations.device
                )
        elif isinstance(self.grain_annotations, list):
            self.grain_annotations = [self.grain_annotations[i] for i in indices_to_keep_in_annotations]
        
        # Update DataFrame and reset index
        self.grain_df = self.grain_df[df_keep_mask].reset_index(drop=True)
        
        # Update grain ID mapping
        new_mapping = {}
        for new_idx, old_idx in enumerate(self.grain_df.index):
            if old_idx in self.grain_id_mapping:
                new_mapping[new_idx] = self.grain_id_mapping[old_idx]
        self.grain_id_mapping = new_mapping
        
        logger.info(f"Updated memory structures: {len(self.grain_df)} grains remaining")
    
    def _update_visualization_after_deletion(self, indices_to_delete: Set[int]):
        """
        Update visualization after grain deletion without full rebuild.
        
        Args:
            indices_to_delete: Set of DataFrame indices that were deleted
        """
        try:
            # Remove deleted grain items from the scene
            if hasattr(self, 'grain_items'):
                for idx in indices_to_delete:
                    if idx in self.grain_items:
                        polygon_item, text_item = self.grain_items[idx]
                        if polygon_item and polygon_item.scene():
                            polygon_item.scene().removeItem(polygon_item)
                        if text_item and text_item.scene():
                            text_item.scene().removeItem(text_item)
                        del self.grain_items[idx]
            
            # Update results widget
            if hasattr(self, 'results_widget'):
                self.results_widget.populate(self.grain_df)
            
            # Redraw remaining grain highlights
            self.draw_grain_highlights()
            
            logger.info("Updated visualization without full rebuild")
            
        except Exception as e:
            logger.warning(f"Failed to update visualization efficiently, falling back to full rebuild: {e}")
            # Fallback to full visualization rebuild
            self._rebuild_visualization()
    
    def _rebuild_visualization(self):
        """
        Rebuild the entire visualization as a fallback.
        """
        try:
            from src.grainsight_components.core.image_utils import create_segmented_visualization
            from src.grainsight_components.gui.widgets import DEFAULT_CONTOUR_THICKNESS
            
            # Get contour thickness from current parameter widget
            current_param_widget = self.model_params_stack.currentWidget()
            contour_thickness = getattr(current_param_widget, 'contour_thickness_slider', None)
            contour_thickness = contour_thickness.value() if contour_thickness else DEFAULT_CONTOUR_THICKNESS
            
            # Recreate visualization
            new_vis = create_segmented_visualization(
                self.grain_uploaded_image, 
                self.grain_annotations, 
                contour_thickness=contour_thickness
            )
            
            if new_vis:
                self.grain_processed_image_vis = new_vis
                self.display_image_on_scene(new_vis)
            else:
                logger.error("Failed to regenerate visualization after deletion")
                self.display_image_on_scene(self.grain_uploaded_image)
            
            # Redraw grain highlights
            self.draw_grain_highlights()
            
        except Exception as e:
            logger.error(f"Failed to rebuild visualization: {e}")
    
    def _delete_grains_legacy_optimized(self, indices_to_delete: Set[int]) -> bool:
        """
        Legacy deletion method with optimizations to avoid unnecessary recalculation.
        
        Args:
            indices_to_delete: Set of DataFrame indices to delete
            
        Returns:
            True if deletion successful, False otherwise
        """
        try:
            # Update in-memory structures
            self._update_memory_after_deletion(indices_to_delete)
            
            # Skip automatic recalculation since we're just deleting
            # The parameters remain valid for the remaining grains
            
            # Update visualization
            self._update_visualization_after_deletion(indices_to_delete)
            
            # Mark state as modified and save
            self.mark_state_as_modified()
            self.save_grain_analysis_state()
            
            self.update_status(f"Deleted {len(indices_to_delete)} grains (legacy optimized).")
            return True
            
        except Exception as e:
            logger.error(f"Legacy optimized deletion failed: {e}")
            return False
    
    def save_grain_analysis_state_optimized(self) -> bool:
        """
        Save grain analysis state using optimized storage when possible.
        
        Returns:
            True if save successful, False otherwise
        """
        if not hasattr(self, 'grain_image_file_path') or not self.grain_image_file_path:
            logger.debug("Cannot save state: No image file path")
            return False
        
        # Skip if nothing has changed
        if not self.state_modified:
            logger.debug("Skipping state save: No changes detected")
            return True
        
        # Get image ID
        image_id = self._get_image_id_for_path(self.grain_image_file_path)
        if not image_id:
            logger.warning(f"Cannot find image ID for {self.grain_image_file_path}")
            return False
        
        try:
            start_time = time.time()
            
            # Initialize optimized storage if not already done
            if not self.optimized_storage:
                if not self._init_optimized_storage(image_id):
                    logger.warning("Failed to initialize optimized storage, using legacy save")
                    return self._save_grain_analysis_state_legacy()
            
            # Check if we should use optimized storage
            if (self.use_optimized_storage and 
                hasattr(self, 'grain_annotations') and self.grain_annotations is not None and
                hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty):
                
                # Save using optimized storage
                success = self.optimized_storage.save_grains(
                    self.grain_annotations,
                    self.grain_df,
                    scale_factor=getattr(self, 'grain_current_scale_factor', None),
                    scale_unit=getattr(self, 'grain_current_scale_unit', 'μm')
                )
                
                if success:
                    # Update grain ID mapping
                    self._update_grain_id_mapping()
                    
                    # Save UI state separately
                    self._save_ui_state(image_id)
                    
                    elapsed_time = time.time() - start_time
                    logger.info(f"Optimized state save completed in {elapsed_time:.3f}s")
                    
                    # Reset modified flag
                    self.state_modified = False
                    return True
                else:
                    logger.warning("Optimized storage save failed, falling back to legacy")
            
            # Fallback to legacy save
            return self._save_grain_analysis_state_legacy()
            
        except Exception as e:
            logger.error(f"Error in optimized state save: {e}")
            return self._save_grain_analysis_state_legacy()
    
    def _update_grain_id_mapping(self):
        """
        Update the mapping between DataFrame indices and grain IDs.
        """
        if not self.optimized_storage:
            return
            
        try:
            # Get grain metadata from storage
            grain_index = self.optimized_storage.grain_index
            active_grains = {gid: gdata for gid, gdata in grain_index['grains'].items() 
                           if gdata.get('active', True)}
            
            # Create mapping from DataFrame index to grain ID
            self.grain_id_mapping = {}
            for df_idx, (grain_id, grain_data) in enumerate(active_grains.items()):
                self.grain_id_mapping[df_idx] = grain_id
            
            logger.debug(f"Updated grain ID mapping for {len(self.grain_id_mapping)} grains")
            
        except Exception as e:
            logger.error(f"Failed to update grain ID mapping: {e}")
    
    def _save_ui_state(self, image_id: str):
        """
        Save UI state separately from grain data.
        
        Args:
            image_id: Image identifier
        """
        try:
            ui_state = {
                'scale_line': self.view.scale_line if hasattr(self, 'view') and hasattr(self.view, 'scale_line') else None,
                'scale_value': self.grain_current_scale_factor if hasattr(self, 'grain_current_scale_factor') else None,
                'scale_unit': self.scale_unit_combo.currentText() if hasattr(self, 'scale_unit_combo') else 'μm',
                'polygons_loaded': self.polygons_loaded if hasattr(self, 'polygons_loaded') else False,
                'ui_timestamp': datetime.now().isoformat(),
            }
            
            # Save UI state to project if available
            if hasattr(self, 'project') and self.project:
                # Store UI state in a separate location
                ui_state_path = os.path.join(
                    self.project.temp_dir, "state", "grain_analysis", image_id, "ui_state.json"
                )
                os.makedirs(os.path.dirname(ui_state_path), exist_ok=True)
                
                import json
                with open(ui_state_path, 'w') as f:
                    json.dump(ui_state, f, indent=2, default=str)
                
                logger.debug(f"Saved UI state for image {image_id}")
            
        except Exception as e:
            logger.error(f"Failed to save UI state: {e}")
    
    def _save_grain_analysis_state_legacy(self) -> bool:
        """
        Fallback to the original save method.
        
        Returns:
            True if save successful, False otherwise
        """
        logger.info("Using legacy state save method")
        # Call the original save method
        return self.save_grain_analysis_state_original()
    
    def load_grain_analysis_state_optimized(self, image_id: str) -> bool:
        """
        Load grain analysis state using optimized storage when available.
        
        Args:
            image_id: Image identifier
            
        Returns:
            True if load successful, False otherwise
        """
        try:
            # Initialize optimized storage
            if not self._init_optimized_storage(image_id):
                logger.warning("Failed to initialize optimized storage for loading")
                return False
            
            # Check if optimized storage has data
            if (self.optimized_storage and 
                self.optimized_storage.grain_index['active_grains'] > 0):
                
                logger.info(f"Loading state using optimized storage for image {image_id}")
                
                # Load active grains
                annotations, dataframe = self.optimized_storage.load_active_grains()
                
                if annotations is not None and dataframe is not None:
                    self.grain_annotations = annotations
                    self.grain_df = dataframe
                    
                    # Update grain ID mapping
                    self._update_grain_id_mapping()
                    
                    # Load UI state
                    self._load_ui_state(image_id)
                    
                    # Update UI
                    if hasattr(self, 'results_widget'):
                        self.results_widget.populate(self.grain_df)
                    
                    self.update_action_states()
                    
                    logger.info(f"Successfully loaded {len(self.grain_df)} grains using optimized storage")
                    return True
                else:
                    logger.warning("Failed to load grains from optimized storage")
            
            # Fallback to legacy loading
            return self._load_grain_analysis_state_legacy(image_id)
            
        except Exception as e:
            logger.error(f"Error in optimized state loading: {e}")
            return self._load_grain_analysis_state_legacy(image_id)
    
    def _load_ui_state(self, image_id: str):
        """
        Load UI state from separate storage.
        
        Args:
            image_id: Image identifier
        """
        try:
            if not hasattr(self, 'project') or not self.project:
                return
                
            ui_state_path = os.path.join(
                self.project.temp_dir, "state", "grain_analysis", image_id, "ui_state.json"
            )
            
            if os.path.exists(ui_state_path):
                import json
                with open(ui_state_path, 'r') as f:
                    ui_state = json.load(f)
                
                # Restore UI state
                if 'scale_value' in ui_state and ui_state['scale_value'] is not None:
                    self.grain_current_scale_factor = ui_state['scale_value']
                    self.update_scale_display()
                
                if 'scale_unit' in ui_state and hasattr(self, 'scale_unit_combo'):
                    index = self.scale_unit_combo.findText(ui_state['scale_unit'])
                    if index >= 0:
                        self.scale_unit_combo.setCurrentIndex(index)
                
                logger.debug(f"Loaded UI state for image {image_id}")
            
        except Exception as e:
            logger.error(f"Failed to load UI state: {e}")
    
    def _load_grain_analysis_state_legacy(self, image_id: str) -> bool:
        """
        Fallback to the original load method.
        
        Args:
            image_id: Image identifier
            
        Returns:
            True if load successful, False otherwise
        """
        logger.info("Using legacy state load method")
        # Call the original load method
        return self.load_grain_analysis_state_original(image_id)
    
    def migrate_to_optimized_storage(self, image_id: str) -> bool:
        """
        Migrate existing grain data to optimized storage format.
        
        Args:
            image_id: Image identifier
            
        Returns:
            True if migration successful, False otherwise
        """
        try:
            if not hasattr(self, 'project') or not self.project:
                logger.warning("Cannot migrate: No project available")
                return False
            
            # Check if legacy files exist
            legacy_dir = os.path.join(self.project.temp_dir, "state", "grain_analysis", image_id)
            annotations_path = os.path.join(legacy_dir, "annotations.npz")
            dataframe_path = os.path.join(legacy_dir, "dataframe.csv")
            
            if not (os.path.exists(annotations_path) and os.path.exists(dataframe_path)):
                logger.info(f"No legacy files to migrate for image {image_id}")
                return True
            
            # Initialize optimized storage
            if not self._init_optimized_storage(image_id):
                logger.error("Failed to initialize optimized storage for migration")
                return False
            
            # Perform migration
            logger.info(f"Migrating legacy grain data to optimized storage for image {image_id}")
            success = self.optimized_storage.migrate_from_legacy(annotations_path, dataframe_path)
            
            if success:
                logger.info(f"Successfully migrated grain data for image {image_id}")
                # Optionally backup or remove legacy files
                # os.rename(annotations_path, annotations_path + ".legacy")
                # os.rename(dataframe_path, dataframe_path + ".legacy")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to migrate to optimized storage: {e}")
            return False
    
    def get_storage_statistics(self) -> Optional[Dict[str, Any]]:
        """
        Get storage statistics for the current image.
        
        Returns:
            Dictionary with storage statistics or None if not available
        """
        if self.optimized_storage:
            return self.optimized_storage.get_storage_stats()
        return None
    
    def cleanup_deleted_grains(self) -> int:
        """
        Clean up physically deleted grain files to free disk space.
        
        Returns:
            Number of files cleaned up
        """
        if self.optimized_storage:
            return self.optimized_storage.cleanup_deleted_grains()
        return 0


# Integration functions to patch the existing GrainAnalysisWidget
def patch_grain_analysis_widget(widget_class):
    """
    Patch the existing GrainAnalysisWidget class with optimized methods.
    
    Args:
        widget_class: The GrainAnalysisWidget class to patch
    """
    # Store original methods
    widget_class.save_grain_analysis_state_original = widget_class.save_grain_analysis_state
    widget_class.delete_selected_grains_original = widget_class.delete_selected_grains
    
    # Add optimized methods
    for attr_name in dir(OptimizedGrainAnalysisWidget):
        if not attr_name.startswith('_') or attr_name.startswith('_init') or attr_name.startswith('_update') or attr_name.startswith('_save') or attr_name.startswith('_load'):
            attr = getattr(OptimizedGrainAnalysisWidget, attr_name)
            if callable(attr):
                setattr(widget_class, attr_name, attr)
    
    # Replace main methods with optimized versions
    def delete_selected_grains_patched(self, indices_to_delete: Set[int]):
        """Patched version that uses optimized deletion when possible."""
        if hasattr(self, 'use_optimized_storage') and self.use_optimized_storage:
            success = self.delete_selected_grains_optimized(indices_to_delete)
            if success:
                return
        # Fallback to original method
        return self.delete_selected_grains_original(indices_to_delete)
    
    def save_grain_analysis_state_patched(self):
        """Patched version that uses optimized saving when possible."""
        if hasattr(self, 'use_optimized_storage') and self.use_optimized_storage:
            success = self.save_grain_analysis_state_optimized()
            if success:
                return success
        # Fallback to original method
        return self.save_grain_analysis_state_original()
    
    # Apply patches
    widget_class.delete_selected_grains = delete_selected_grains_patched
    widget_class.save_grain_analysis_state = save_grain_analysis_state_patched
    
    logger.info("Successfully patched GrainAnalysisWidget with optimized storage methods")