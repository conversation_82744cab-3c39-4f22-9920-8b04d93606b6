# src/core/grain_storage_integration.py
"""
Integration module that applies optimized grain storage to the existing
GrainAnalysisWidget without breaking backward compatibility.

This module provides a seamless way to enable the optimized storage system
while maintaining full compatibility with existing projects and workflows.
"""

import os
import logging
from typing import Optional, Any
from functools import wraps

logger = logging.getLogger(__name__)

def patch_project_grain_storage(project_class):
    """
    Patch the GrainSightProject class to use optimized storage when available.
    
    Args:
        project_class: The GrainSightProject class to patch
    """
    # Store original methods
    project_class.save_grain_analysis_state_original = project_class.save_grain_analysis_state
    project_class.load_grain_analysis_state_original = project_class.load_grain_analysis_state
    
    def save_grain_analysis_state_optimized(self, image_id: str, state: dict):
        """
        Optimized save method that uses optimized storage when available.
        """
        try:
            # Check if we have optimized storage available
            from src.core.optimized_grain_storage import OptimizedGrainStorage
            
            if hasattr(self, 'temp_dir') and self.temp_dir:
                base_dir = os.path.join(self.temp_dir, "state")
                
                # Ensure the base directory exists
                os.makedirs(base_dir, exist_ok=True)
                
                storage = OptimizedGrainStorage(base_dir, image_id)
                
                # Extract annotations and dataframe from state
                annotations = state.get('annotations')
                df = state.get('df')
                scale_factor = state.get('scale_value', 1.0)
                scale_unit = state.get('scale_unit', 'μm')
                
                if annotations is not None and df is not None:
                    # Try to save using optimized storage
                    success = storage.save_grains(
                        annotations, df, 
                        scale_factor=scale_factor, 
                        scale_unit=scale_unit
                    )
                    
                    if success:
                        # Save the rest of the state (non-grain data) using legacy method
                        legacy_state = {k: v for k, v in state.items() 
                                      if k not in ['annotations', 'df']}
                        if legacy_state:
                            self.save_grain_analysis_state_original(image_id, legacy_state)
                        
                        # Update in-memory state
                        self.grain_analysis_states[image_id] = state
                        logger.info(f"Successfully saved grain analysis state using optimized storage for {image_id}")
                        return
            
            # Fallback to original method
            self.save_grain_analysis_state_original(image_id, state)
            
        except Exception as e:
            logger.error(f"Error in optimized save for {image_id}: {e}")
            # Fallback to original method
            self.save_grain_analysis_state_original(image_id, state)
    
    def load_grain_analysis_state_optimized(self, image_id: str, load_annotations: bool = False):
        """
        Optimized load method that uses optimized storage when available.
        """
        try:
            # Check if we have optimized storage available
            from src.core.optimized_grain_storage import OptimizedGrainStorage
            
            if hasattr(self, 'temp_dir') and self.temp_dir:
                base_dir = os.path.join(self.temp_dir, "state")
                storage = OptimizedGrainStorage(base_dir, image_id)
                
                # Check if optimized storage has data
                stats = storage.get_storage_stats()
                if stats['total_grains'] > 0:
                    # Load from optimized storage
                    if load_annotations:
                        annotations, df = storage.load_active_grains()
                        if annotations is not None and df is not None:
                            # Create state from optimized storage
                            state = {
                                'annotations': annotations,
                                'df': df,
                                'has_results': True,
                                'num_grains': len(df)
                            }
                            
                            # Load any additional state data from legacy storage
                            legacy_state = self.load_grain_analysis_state_original(image_id, load_annotations=False)
                            if legacy_state:
                                # Merge non-grain data from legacy state
                                for key, value in legacy_state.items():
                                    if key not in ['annotations', 'df']:
                                        state[key] = value
                            
                            # Update in-memory state
                            self.grain_analysis_states[image_id] = state
                            logger.info(f"Loaded grain analysis state using optimized storage for {image_id}")
                            return state
                    else:
                        # Load metadata only
                        state = {
                            'has_results': True,
                            'num_grains': stats['active_grains']
                        }
                        
                        # Load additional metadata from legacy storage
                        legacy_state = self.load_grain_analysis_state_original(image_id, load_annotations=False)
                        if legacy_state:
                            for key, value in legacy_state.items():
                                if key not in ['annotations', 'df']:
                                    state[key] = value
                        
                        self.grain_analysis_states[image_id] = state
                        return state
            
            # Fallback to original method
            return self.load_grain_analysis_state_original(image_id, load_annotations)
            
        except Exception as e:
            logger.error(f"Error in optimized load for {image_id}: {e}")
            # Fallback to original method
            return self.load_grain_analysis_state_original(image_id, load_annotations)
    
    # Replace the methods
    project_class.save_grain_analysis_state = save_grain_analysis_state_optimized
    project_class.load_grain_analysis_state = load_grain_analysis_state_optimized
    
    logger.info("Patched GrainSightProject with optimized storage methods")

def enable_optimized_grain_storage():
    """
    Enable optimized grain storage for the application.
    This function should be called during application initialization.
    """
    try:
        # Import the widget class and optimization
        from src.gui.grain_analysis_widget import GrainAnalysisWidget
        from src.gui.grain_analysis_widget_optimized import (
            OptimizedGrainAnalysisWidget, 
            patch_grain_analysis_widget
        )
        from src.core.grainsight_project import VisionLabProject
        
        # Apply the optimization patch to the widget
        patch_grain_analysis_widget(GrainAnalysisWidget)
        
        # Add initialization method to the widget
        original_init = GrainAnalysisWidget.__init__
        
        @wraps(original_init)
        def enhanced_init(self, *args, **kwargs):
            # Call original initialization
            original_init(self, *args, **kwargs)
            
            # Initialize optimized storage components
            self.optimized_storage = None
            self.use_optimized_storage = True
            self.grain_id_mapping = {}
            
            logger.info("Initialized GrainAnalysisWidget with optimized storage support")
        
        GrainAnalysisWidget.__init__ = enhanced_init
        
        # Patch the project's methods to use optimized storage
        patch_project_grain_storage(VisionLabProject)
        
        logger.info("Successfully enabled optimized grain storage system")
        return True
        
    except ImportError as e:
        logger.error(f"Failed to import required modules for optimization: {e}")
        return False
    except Exception as e:
        logger.error(f"Failed to enable optimized grain storage: {e}")
        return False

def create_performance_monitor():
    """
    Create a performance monitoring decorator for grain operations.
    """
    def performance_monitor(operation_name: str):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                import time
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    elapsed_time = time.time() - start_time
                    
                    # Log performance metrics
                    logger.info(f"{operation_name} completed in {elapsed_time:.3f}s")
                    
                    # Update performance statistics if widget has them
                    if hasattr(args[0], '_performance_stats'):
                        if not hasattr(args[0], '_performance_stats'):
                            args[0]._performance_stats = {}
                        args[0]._performance_stats[operation_name] = elapsed_time
                    
                    return result
                    
                except Exception as e:
                    elapsed_time = time.time() - start_time
                    logger.error(f"{operation_name} failed after {elapsed_time:.3f}s: {e}")
                    raise
            
            return wrapper
        return decorator
    return performance_monitor

def create_migration_utility():
    """
    Create a utility class for migrating existing projects to optimized storage.
    """
    class GrainStorageMigrationUtility:
        """
        Utility for migrating existing grain analysis projects to optimized storage.
        """
        
        def __init__(self, project_path: str):
            self.project_path = project_path
            self.migration_log = []
        
        def scan_for_legacy_data(self) -> dict:
            """
            Scan the project for legacy grain analysis data that can be migrated.
            
            Returns:
                Dictionary with migration candidates
            """
            candidates = {}
            
            try:
                state_dir = os.path.join(self.project_path, "state", "grain_analysis")
                
                if not os.path.exists(state_dir):
                    logger.info("No grain analysis state directory found")
                    return candidates
                
                for image_dir in os.listdir(state_dir):
                    image_path = os.path.join(state_dir, image_dir)
                    
                    if os.path.isdir(image_path):
                        # Check for legacy files
                        annotations_file = os.path.join(image_path, "annotations.npz")
                        dataframe_file = os.path.join(image_path, "dataframe.csv")
                        
                        # Check for optimized storage
                        grain_index_file = os.path.join(image_path, "grain_index.json")
                        
                        if (os.path.exists(annotations_file) and 
                            os.path.exists(dataframe_file) and 
                            not os.path.exists(grain_index_file)):
                            
                            # Calculate file sizes
                            annotations_size = os.path.getsize(annotations_file)
                            dataframe_size = os.path.getsize(dataframe_file)
                            
                            candidates[image_dir] = {
                                'annotations_file': annotations_file,
                                'dataframe_file': dataframe_file,
                                'annotations_size': annotations_size,
                                'dataframe_size': dataframe_size,
                                'total_size': annotations_size + dataframe_size,
                                'can_migrate': True
                            }
                
                logger.info(f"Found {len(candidates)} migration candidates")
                return candidates
                
            except Exception as e:
                logger.error(f"Error scanning for legacy data: {e}")
                return {}
        
        def migrate_image_data(self, image_id: str, force: bool = False) -> bool:
            """
            Migrate a specific image's data to optimized storage.
            
            Args:
                image_id: Image identifier
                force: Force migration even if optimized data exists
                
            Returns:
                True if migration successful, False otherwise
            """
            try:
                from src.core.optimized_grain_storage import OptimizedGrainStorage
                
                # Initialize optimized storage
                storage = OptimizedGrainStorage(self.project_path, image_id)
                
                # Check if already migrated
                if not force and storage.grain_index['total_grains'] > 0:
                    logger.info(f"Image {image_id} already has optimized storage")
                    return True
                
                # Find legacy files
                legacy_dir = os.path.join(self.project_path, "state", "grain_analysis", image_id)
                annotations_path = os.path.join(legacy_dir, "annotations.npz")
                dataframe_path = os.path.join(legacy_dir, "dataframe.csv")
                
                if not (os.path.exists(annotations_path) and os.path.exists(dataframe_path)):
                    logger.warning(f"Legacy files not found for image {image_id}")
                    return False
                
                # Perform migration
                logger.info(f"Migrating image {image_id} to optimized storage")
                success = storage.migrate_from_legacy(annotations_path, dataframe_path)
                
                if success:
                    self.migration_log.append({
                        'image_id': image_id,
                        'status': 'success',
                        'timestamp': time.time()
                    })
                    logger.info(f"Successfully migrated image {image_id}")
                else:
                    self.migration_log.append({
                        'image_id': image_id,
                        'status': 'failed',
                        'timestamp': time.time()
                    })
                    logger.error(f"Failed to migrate image {image_id}")
                
                return success
                
            except Exception as e:
                logger.error(f"Error migrating image {image_id}: {e}")
                self.migration_log.append({
                    'image_id': image_id,
                    'status': 'error',
                    'error': str(e),
                    'timestamp': time.time()
                })
                return False
        
        def migrate_all_candidates(self, candidates: dict = None) -> dict:
            """
            Migrate all migration candidates to optimized storage.
            
            Args:
                candidates: Dictionary of candidates (if None, will scan)
                
            Returns:
                Dictionary with migration results
            """
            if candidates is None:
                candidates = self.scan_for_legacy_data()
            
            results = {
                'total': len(candidates),
                'successful': 0,
                'failed': 0,
                'errors': []
            }
            
            for image_id in candidates:
                try:
                    success = self.migrate_image_data(image_id)
                    if success:
                        results['successful'] += 1
                    else:
                        results['failed'] += 1
                except Exception as e:
                    results['failed'] += 1
                    results['errors'].append(f"Image {image_id}: {e}")
            
            logger.info(f"Migration completed: {results['successful']}/{results['total']} successful")
            return results
        
        def get_migration_report(self) -> str:
            """
            Generate a migration report.
            
            Returns:
                Formatted migration report
            """
            if not self.migration_log:
                return "No migrations performed yet."
            
            successful = sum(1 for entry in self.migration_log if entry['status'] == 'success')
            failed = sum(1 for entry in self.migration_log if entry['status'] in ['failed', 'error'])
            
            report = f"Migration Report\n"
            report += f"================\n"
            report += f"Total migrations: {len(self.migration_log)}\n"
            report += f"Successful: {successful}\n"
            report += f"Failed: {failed}\n\n"
            
            if failed > 0:
                report += "Failed migrations:\n"
                for entry in self.migration_log:
                    if entry['status'] in ['failed', 'error']:
                        report += f"- {entry['image_id']}: {entry.get('error', 'Unknown error')}\n"
            
            return report
    
    return GrainStorageMigrationUtility

def create_storage_manager():
    """
    Create a storage manager for handling optimized grain storage operations.
    """
    class OptimizedStorageManager:
        """
        Manager for optimized grain storage operations across the application.
        """
        
        def __init__(self):
            self.active_storages = {}  # image_id -> OptimizedGrainStorage
            self.performance_stats = {}
        
        def get_storage(self, project_path: str, image_id: str):
            """
            Get or create optimized storage for an image.
            
            Args:
                project_path: Path to the project
                image_id: Image identifier
                
            Returns:
                OptimizedGrainStorage instance
            """
            storage_key = f"{project_path}:{image_id}"
            
            if storage_key not in self.active_storages:
                from src.core.optimized_grain_storage import OptimizedGrainStorage
                base_dir = os.path.join(project_path, "state")
                self.active_storages[storage_key] = OptimizedGrainStorage(base_dir, image_id)
            
            return self.active_storages[storage_key]
        
        def cleanup_storage(self, project_path: str, image_id: str) -> int:
            """
            Clean up deleted grains for a specific image.
            
            Args:
                project_path: Path to the project
                image_id: Image identifier
                
            Returns:
                Number of files cleaned up
            """
            storage = self.get_storage(project_path, image_id)
            return storage.cleanup_deleted_grains()
        
        def get_global_statistics(self) -> dict:
            """
            Get global storage statistics across all active storages.
            
            Returns:
                Dictionary with global statistics
            """
            global_stats = {
                'total_storages': len(self.active_storages),
                'total_grains': 0,
                'total_active_grains': 0,
                'total_deleted_grains': 0,
                'total_size_bytes': 0,
                'storage_efficiency': 0
            }
            
            total_size = 0
            active_size = 0
            
            for storage in self.active_storages.values():
                stats = storage.get_storage_stats()
                global_stats['total_grains'] += stats['total_grains']
                global_stats['total_active_grains'] += stats['active_grains']
                global_stats['total_deleted_grains'] += stats['deleted_grains']
                global_stats['total_size_bytes'] += stats['total_size_bytes']
                
                total_size += stats['total_size_bytes']
                active_size += stats['active_size_bytes']
            
            if total_size > 0:
                global_stats['storage_efficiency'] = (active_size / total_size) * 100
            
            return global_stats
        
        def cleanup_all_storages(self) -> dict:
            """
            Clean up deleted grains across all active storages.
            
            Returns:
                Dictionary with cleanup results
            """
            results = {
                'total_storages': len(self.active_storages),
                'total_files_cleaned': 0,
                'errors': []
            }
            
            for storage_key, storage in self.active_storages.items():
                try:
                    cleaned = storage.cleanup_deleted_grains()
                    results['total_files_cleaned'] += cleaned
                    logger.info(f"Cleaned {cleaned} files from storage {storage_key}")
                except Exception as e:
                    error_msg = f"Failed to cleanup storage {storage_key}: {e}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            return results
    
    return OptimizedStorageManager

# Global storage manager instance
_storage_manager = None

def get_storage_manager():
    """
    Get the global storage manager instance.
    
    Returns:
        OptimizedStorageManager instance
    """
    global _storage_manager
    if _storage_manager is None:
        StorageManager = create_storage_manager()
        _storage_manager = StorageManager()
    return _storage_manager

def initialize_optimized_storage_system():
    """
    Initialize the complete optimized storage system.
    This should be called during application startup.
    
    Returns:
        True if initialization successful, False otherwise
    """
    try:
        # Enable optimized grain storage
        if not enable_optimized_grain_storage():
            logger.error("Failed to enable optimized grain storage")
            return False
        
        # Initialize global storage manager
        storage_manager = get_storage_manager()
        logger.info("Initialized global storage manager")
        
        # Create performance monitor
        performance_monitor = create_performance_monitor()
        logger.info("Created performance monitoring system")
        
        logger.info("Optimized grain storage system initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize optimized storage system: {e}")
        return False

if __name__ == "__main__":
    # Test the integration system
    logging.basicConfig(level=logging.INFO)
    
    success = initialize_optimized_storage_system()
    if success:
        print("✓ Optimized storage system initialized successfully")
    else:
        print("✗ Failed to initialize optimized storage system")