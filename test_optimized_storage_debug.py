#!/usr/bin/env python3
"""
Test script to debug optimized storage functionality
"""

import os
import sys
import logging
import tempfile
import numpy as np
import pandas as pd
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_optimized_storage():
    """Test the optimized storage system directly."""
    try:
        # Import the optimized storage
        from src.core.optimized_grain_storage import OptimizedGrainStorage
        from src.core.grain_storage_integration import initialize_optimized_storage_system
        
        logger.info("Testing optimized storage system...")
        
        # Initialize the system
        logger.info("Initializing optimized storage system...")
        success = initialize_optimized_storage_system()
        logger.info(f"Initialization result: {success}")
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info(f"Using temporary directory: {temp_dir}")
            
            # Create test data
            image_id = "test_image_001"
            base_dir = os.path.join(temp_dir, "state")
            
            # Create some test annotations and dataframe
            annotations = [np.random.rand(100, 100) for _ in range(5)]
            df = pd.DataFrame({
                'area': [100, 200, 150, 300, 250],
                'perimeter': [40, 60, 50, 70, 65],
                'circularity': [0.8, 0.7, 0.9, 0.6, 0.75]
            })
            
            logger.info(f"Created test data: {len(annotations)} annotations, DataFrame shape: {df.shape}")
            
            # Test the storage
            storage = OptimizedGrainStorage(base_dir, image_id)
            logger.info(f"Created OptimizedGrainStorage with base_dir: {base_dir}, image_id: {image_id}")
            
            # Test saving
            logger.info("Testing save_grains...")
            save_success = storage.save_grains(annotations, df, scale_factor=1.0, scale_unit="μm")
            logger.info(f"Save result: {save_success}")
            
            if save_success:
                # Test loading
                logger.info("Testing load_active_grains...")
                loaded_annotations, loaded_df = storage.load_active_grains()
                
                if loaded_annotations is not None and loaded_df is not None:
                    logger.info(f"Loaded {len(loaded_annotations)} annotations and DataFrame with shape {loaded_df.shape}")
                    logger.info("✅ Optimized storage test PASSED")
                    
                    # Check storage stats
                    stats = storage.get_storage_stats()
                    logger.info(f"Storage stats: {stats}")
                    
                    return True
                else:
                    logger.error("❌ Failed to load data from optimized storage")
                    return False
            else:
                logger.error("❌ Failed to save data to optimized storage")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error testing optimized storage: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_project_patching():
    """Test if the project patching is working correctly."""
    try:
        logger.info("Testing project patching...")
        
        # Import the project class
        from src.core.grainsight_project import VisionLabProject
        from src.core.grain_storage_integration import patch_project_grain_storage
        
        # Check if the project has the original methods
        logger.info(f"VisionLabProject has save_grain_analysis_state: {hasattr(VisionLabProject, 'save_grain_analysis_state')}")
        logger.info(f"VisionLabProject has load_grain_analysis_state: {hasattr(VisionLabProject, 'load_grain_analysis_state')}")
        
        # Apply the patch
        logger.info("Applying project patch...")
        patch_project_grain_storage(VisionLabProject)
        
        # Check if the patched methods exist
        logger.info(f"VisionLabProject has save_grain_analysis_state_original: {hasattr(VisionLabProject, 'save_grain_analysis_state_original')}")
        logger.info(f"VisionLabProject has load_grain_analysis_state_original: {hasattr(VisionLabProject, 'load_grain_analysis_state_original')}")
        
        # Create a test project instance
        with tempfile.TemporaryDirectory() as temp_dir:
            project_file = os.path.join(temp_dir, "test_project.vlp")
            project = VisionLabProject(project_file)
            
            logger.info(f"Created test project with temp_dir: {project.temp_dir}")
            logger.info(f"temp_dir exists: {project.temp_dir and os.path.exists(project.temp_dir)}")
            
            # Test the patched save method
            test_state = {
                'annotations': [np.random.rand(50, 50) for _ in range(3)],
                'df': pd.DataFrame({
                    'area': [100, 200, 150],
                    'perimeter': [40, 60, 50]
                }),
                'scale_value': 1.0,
                'scale_unit': 'μm',
                'other_data': 'test_value'
            }
            
            logger.info("Testing patched save method...")
            project.save_grain_analysis_state("test_image", test_state)
            
            logger.info("✅ Project patching test completed")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error testing project patching: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    logger.info("Starting optimized storage debug tests...")
    
    # Test 1: Basic optimized storage functionality
    logger.info("=" * 50)
    logger.info("TEST 1: Basic Optimized Storage")
    logger.info("=" * 50)
    storage_test_result = test_optimized_storage()
    
    # Test 2: Project patching
    logger.info("=" * 50)
    logger.info("TEST 2: Project Patching")
    logger.info("=" * 50)
    patching_test_result = test_project_patching()
    
    # Summary
    logger.info("=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Optimized Storage Test: {'✅ PASSED' if storage_test_result else '❌ FAILED'}")
    logger.info(f"Project Patching Test: {'✅ PASSED' if patching_test_result else '❌ FAILED'}")
    
    if storage_test_result and patching_test_result:
        logger.info("🎉 All tests PASSED! Optimized storage should be working.")
    else:
        logger.info("⚠️  Some tests FAILED. Check the logs above for details.")